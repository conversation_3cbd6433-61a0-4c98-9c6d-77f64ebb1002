/**
 * 优惠梯度叠加
 * 根据Shopline Storefront API - productDiscounts 获取到商品自动折扣活动信息
 * 在商品已有的折扣基础上叠加，并更新在页面：商品详情页、搜索结果页、分类商品列表页、首页商品列表组件处的商品价格、折扣、原价信息
 */

async function getBasicProductDiscounts() {
  const url =
    "https://caguuu.myshopline.com/storefront/graph/v20250601/graphql.json";

    const headers = {
      'content-type': 'application/json',
      'authorization': 'Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJ5c291bCIsInRva2VuIjoiZGFlNWZmZTk0OGNiMjIwNGYzYzNjNTlkZjgwMGIzY2QzYWUyMGU1Y3w4ODUzNXwxNzI2NDY0MTkyNDI3fDE3NDY3NzE2NjB8MjA2MTU1Nzk2MnwifQ.eYX30QQX4a-hCvA8u_yhfMLzsCvn7DwzkFvLPeZZkSu05HlwuUTA4vXJ2DHpuDinVbLvM-mHeGgV0tUcbXW8Kg'
    };

  const body = {
    query: `query GetBasicProductDiscounts($p1: ProductDiscountsInput!) {
  p1: productDiscounts(productDiscountsInput: $p1) {
    productId
    presentCurrency
    buyerId
    autoDiscountActivities {
      activityName
      activitySeq
      discountStyleText
      benefitConditions {
        benefit {
          benefitCount
          benefitAmount
          benefitValueType
          discount
          fixedPrice
          offPercent
          fixedPrice
          promotionSeq
        }
        benefitEvent {
          isAccumulated
          minThreshold
        }
      }
      benefitScopeType
    }
  }
}`,
    variables: {
      p1: {
        discountsProduct: {
          productId: "gid://shopline/Product/16067821077617964975424095",
        },
      },
    },
    operationName: "GetBasicProductDiscounts",
  };

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: headers,
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    const extractedData = extractDiscountData(data);
    return extractedData;
  } catch (error) {
    console.error("Error fetching product discounts:", error);
    
    return {
      productId,
      discounts: [],
      summary: {
        totalActivities: 0,
        bestDiscount: 0
      },
      error: error.message
    };
  }
}

function extractDiscountData(responseData) {
  const p1 = responseData.data?.p1;
  if (!p1 || !p1.autoDiscountActivities) {
    return { discounts: [], productId: null };
  }

  const productId = p1.productId;
  const activities = p1.autoDiscountActivities;

  const extractedDiscounts = activities.map(activity => {
    const discountTiers = activity.benefitConditions.map(condition => ({
      minThreshold: parseInt(condition.benefitEvent.minThreshold),
      minThresholdText: parseInt(condition.benefitEvent.minThreshold) / 100,
      discount: condition.benefit.discount,
      discountText: `${100 - condition.benefit.discount}% OFF`
    })).sort((a, b) => a.minThreshold - b.minThreshold);

    return {
      activityName: activity.activityName,
      activitySeq: activity.activitySeq,
      discountStyleText: activity.discountStyleText,
      discountTiers: discountTiers
    };
  });

  return {
    productId,
    discounts: extractedDiscounts,
    summary: {
      totalActivities: activities.length,
      bestDiscount: Math.max(...activities.flatMap(a => 
        a.benefitConditions.map(c => c.benefit.discount)
      ))
    }
  };
}

// 使用示例
getBasicProductDiscounts()
  .then(data => {
    console.log('提取的折扣数据:', data);
    
    // 输出格式化的折扣信息
    data.discounts.forEach(discount => {
      console.log(`活动: ${discount.activityName}`);
      console.log(`描述: ${discount.discountStyleText}`);
      discount.discountTiers.forEach(tier => {
        console.log(`  购买满 ${tier.minThresholdText} 可享受 ${tier.discountText}`);
      });
    });
  })
  .catch(error => {
    console.error('Failed to fetch data:', error);
  });