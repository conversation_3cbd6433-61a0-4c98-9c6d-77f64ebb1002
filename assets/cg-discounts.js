/**
 * 优惠梯度叠加
 * 根据Shopline Storefront API - productDiscounts 获取到商品自动折扣活动信息
 * 在商品已有的折扣基础上叠加，并更新在页面：商品详情页、搜索结果页、分类商品列表页、首页商品列表组件处的商品价格、折扣、原价信息
 *
 * 使用 MutationObserver 监听页面上 card__badge 元素的变化，自动处理商品折扣信息的更新
 */

// 全局商品信息存储
const productInfoMap = new Map();

// 配置对象
const CONFIG = {
  // API配置
  API_URL: "https://caguuu.myshopline.com/storefront/graph/v20250601/graphql.json",
  API_TOKEN: "Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJ5c291bCIsInRva2VuIjoiZGFlNWZmZTk0OGNiMjIwNGYzYzNjNTlkZjgwMGIzY2QzYWUyMGU1Y3w4ODUzNXwxNzI2NDY0MTkyNDI3fDE3NDY3NzE2NjB8MjA2MTU1Nzk2MnwifQ.eYX30QQX4a-hCvA8u_yhfMLzsCvn7DwzkFvLPeZZkSu05HlwuUTA4vXJ2DHpuDinVbLvM-mHeGgV0tUcbXW8Kg",

  // DOM选择器
  SELECTORS: {
    BADGE: '.card__badge',
    PRODUCT_CARD: '[data-product-id]',
    PRICE_SALE: '.price-item--sale',
    PRICE_REGULAR: '.price-item--regular',
    PRICE_CONTAINER: '.price__container'
  },

  // 货币格式化
  CURRENCY: {
    SYMBOL: '¥',
    LOCALE: 'ja-JP'
  }
};

/**
 * 获取商品折扣信息
 * @param {string} productId - 商品ID
 * @returns {Promise<Object>} 折扣信息对象
 */
async function getBasicProductDiscounts(productId) {
  const headers = {
    'content-type': 'application/json',
    'authorization': CONFIG.API_TOKEN
  };

  const body = {
    query: `query GetBasicProductDiscounts($p1: ProductDiscountsInput!) {
  p1: productDiscounts(productDiscountsInput: $p1) {
    productId
    presentCurrency
    buyerId
    autoDiscountActivities {
      activityName
      activitySeq
      discountStyleText
      benefitConditions {
        benefit {
          benefitCount
          benefitAmount
          benefitValueType
          discount
          fixedPrice
          offPercent
          fixedPrice
          promotionSeq
        }
        benefitEvent {
          isAccumulated
          minThreshold
        }
      }
      benefitScopeType
    }
  }
}`,
    variables: {
      p1: {
        discountsProduct: {
          productId: `gid://shopline/Product/${productId}`,
        },
      },
    },
    operationName: "GetBasicProductDiscounts",
  };

  try {
    const response = await fetch(CONFIG.API_URL, {
      method: "POST",
      headers: headers,
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    const extractedData = extractDiscountData(data);
    return extractedData;
  } catch (error) {
    console.error("Error fetching product discounts:", error);

    return {
      productId,
      discounts: [],
      summary: {
        totalActivities: 0,
        bestDiscount: 0
      },
      error: error.message
    };
  }
}

function extractDiscountData(responseData) {
  const p1 = responseData.data?.p1;
  if (!p1 || !p1.autoDiscountActivities) {
    return { discounts: [], productId: null };
  }

  const productId = p1.productId;
  const activities = p1.autoDiscountActivities;

  const extractedDiscounts = activities.map(activity => {
    const discountTiers = activity.benefitConditions.map(condition => ({
      minThreshold: parseInt(condition.benefitEvent.minThreshold),
      minThresholdText: parseInt(condition.benefitEvent.minThreshold) / 100,
      discount: condition.benefit.discount,
      discountText: `${100 - condition.benefit.discount}% OFF`
    })).sort((a, b) => a.minThreshold - b.minThreshold);

    return {
      activityName: activity.activityName,
      activitySeq: activity.activitySeq,
      discountStyleText: activity.discountStyleText,
      discountTiers: discountTiers
    };
  });

  return {
    productId,
    discounts: extractedDiscounts,
    summary: {
      totalActivities: activities.length,
      bestDiscount: Math.max(...activities.flatMap(a =>
        a.benefitConditions.map(c => c.benefit.discount)
      ))
    }
  };
}

/**
 * 从商品卡片DOM中解析商品信息
 * @param {Element} productCard - 包含data-product-id的商品卡片元素
 * @returns {Object} 商品信息对象
 */
function parseProductInfoFromDOM(productCard) {
  const productId = productCard.getAttribute('data-product-id');
  if (!productId) {
    console.warn('Product card missing data-product-id attribute');
    return null;
  }

  // 获取商品标题
  const titleElement = productCard.querySelector('.product__title');
  const title = titleElement ? titleElement.textContent.trim() : '';

  // 获取价格信息
  const priceContainer = productCard.querySelector(CONFIG.SELECTORS.PRICE_CONTAINER);
  let originalPrice = 0;
  let salePrice = 0;
  let currentDiscount = 0;

  if (priceContainer) {
    // 解析售价
    const salePriceElement = priceContainer.querySelector(CONFIG.SELECTORS.PRICE_SALE);
    if (salePriceElement) {
      const salePriceText = salePriceElement.textContent.replace(/[^\d]/g, '');
      salePrice = parseInt(salePriceText) || 0;
    }

    // 解析原价
    const regularPriceElement = priceContainer.querySelector(CONFIG.SELECTORS.PRICE_REGULAR);
    if (regularPriceElement) {
      const regularPriceText = regularPriceElement.textContent.replace(/[^\d]/g, '');
      originalPrice = parseInt(regularPriceText) || 0;
    }
  }

  // 如果没有原价，使用售价作为原价
  if (originalPrice === 0 && salePrice > 0) {
    originalPrice = salePrice;
  }

  // 计算当前折扣率
  if (originalPrice > 0 && salePrice > 0 && salePrice < originalPrice) {
    currentDiscount = Math.round((1 - salePrice / originalPrice) * 100);
  }

  // 获取折扣徽章信息
  const badgeElement = productCard.querySelector(CONFIG.SELECTORS.BADGE);
  let badgeText = '';
  if (badgeElement) {
    badgeText = badgeElement.textContent.trim();
  }

  return {
    productId,
    title,
    originalPrice,
    salePrice,
    currentDiscount,
    badgeText,
    domElements: {
      productCard,
      priceContainer,
      badgeElement,
      salePriceElement: priceContainer?.querySelector(CONFIG.SELECTORS.PRICE_SALE),
      regularPriceElement: priceContainer?.querySelector(CONFIG.SELECTORS.PRICE_REGULAR)
    }
  };
}

/**
 * 计算应用折扣后的价格
 * @param {Object} productInfo - 商品信息
 * @param {Object} discountData - 折扣数据
 * @returns {Object} 计算后的价格信息
 */
function calculateDiscountedPrices(productInfo, discountData) {
  if (!discountData.discounts || discountData.discounts.length === 0) {
    return {
      finalSalePrice: productInfo.salePrice,
      finalOriginalPrice: productInfo.originalPrice,
      finalDiscount: productInfo.currentDiscount,
      appliedDiscounts: [],
      hasApiDiscount: false
    };
  }

  let bestApiDiscount = 0;
  let appliedDiscounts = [];

  // 找到最佳API折扣
  discountData.discounts.forEach(discount => {
    discount.discountTiers.forEach(tier => {
      // 这里可以根据购买数量或金额来判断是否满足条件
      // 暂时使用最大折扣
      const discountPercent = 100 - tier.discount;
      if (discountPercent > bestApiDiscount) {
        bestApiDiscount = discountPercent;
        appliedDiscounts = [{
          activityName: discount.activityName,
          discountPercent: discountPercent,
          discountText: tier.discountText
        }];
      }
    });
  });

  // 重要：不要叠加折扣，而是选择更好的折扣
  // 比较当前折扣和API折扣，选择更优的那个
  let finalDiscount = productInfo.currentDiscount;
  let finalSalePrice = productInfo.salePrice;
  let hasApiDiscount = false;

  if (bestApiDiscount > 0) {
    // 计算API折扣后的价格
    const apiDiscountPrice = Math.round(productInfo.originalPrice * (100 - bestApiDiscount) / 100);

    // 如果API折扣价格更低，使用API折扣
    if (apiDiscountPrice < productInfo.salePrice) {
      finalSalePrice = apiDiscountPrice;
      finalDiscount = bestApiDiscount;
      hasApiDiscount = true;
    }
    // 否则保持原有的价格和折扣
  }

  return {
    finalSalePrice,
    finalOriginalPrice: productInfo.originalPrice,
    finalDiscount,
    appliedDiscounts,
    hasApiDiscount
  };
}

/**
 * 格式化价格显示
 * @param {number} price - 价格
 * @returns {string} 格式化后的价格字符串
 */
function formatPrice(price) {
  return new Intl.NumberFormat(CONFIG.CURRENCY.LOCALE, {
    style: 'currency',
    currency: 'JPY',
    minimumFractionDigits: 0
  }).format(price);
}

/**
 * 更新商品卡片的价格和折扣显示
 * @param {Object} productInfo - 商品信息
 * @param {Object} priceData - 计算后的价格数据
 */
function updateProductCardDisplay(productInfo, priceData) {
  const { domElements } = productInfo;

  try {
    // 更新售价
    if (domElements.salePriceElement) {
      const formattedSalePrice = formatPrice(priceData.finalSalePrice);
      // 保留原有的税费信息
      const taxInfo = domElements.salePriceElement.querySelector('p');
      domElements.salePriceElement.innerHTML = `${formattedSalePrice}${taxInfo ? taxInfo.outerHTML : ''} ~`;
    }

    // 更新原价
    if (domElements.regularPriceElement && priceData.finalSalePrice < priceData.finalOriginalPrice) {
      const formattedOriginalPrice = formatPrice(priceData.finalOriginalPrice);
      domElements.regularPriceElement.textContent = formattedOriginalPrice;
    }

    // 更新折扣徽章
    if (domElements.badgeElement && priceData.finalDiscount > 0) {
      const badgeSpan = domElements.badgeElement.querySelector('span');
      if (badgeSpan) {
        badgeSpan.textContent = `${priceData.finalDiscount}％OFF`;
      }
    }

    console.log(`Updated product ${productInfo.productId}: ${formatPrice(priceData.finalSalePrice)} (${priceData.finalDiscount}% OFF)`);
  } catch (error) {
    console.error('Error updating product card display:', error);
  }
}

/**
 * 处理单个商品的折扣更新
 * @param {Element} productCard - 商品卡片元素
 */
async function processProductDiscount(productCard) {
  try {
    // 解析商品信息
    const productInfo = parseProductInfoFromDOM(productCard);
    if (!productInfo) {
      return;
    }

    // 检查是否已经处理过该商品
    const existingInfo = productInfoMap.get(productInfo.productId);

    // 如果已经应用过API折扣，且商品信息没有显著变化，则跳过
    if (existingInfo && existingInfo.hasApiDiscount) {
      // 检查原始价格是否发生变化（这可能意味着商品信息更新了）
      if (existingInfo.originalPrice === productInfo.originalPrice) {
        console.log(`Product ${productInfo.productId} already processed with API discount, skipping`);
        return;
      }
    }

    // 如果商品信息完全相同，也跳过处理
    if (existingInfo &&
        existingInfo.salePrice === productInfo.salePrice &&
        existingInfo.originalPrice === productInfo.originalPrice &&
        existingInfo.badgeText === productInfo.badgeText) {
      return;
    }

    console.log(`Processing product ${productInfo.productId}...`);

    // 获取折扣信息
    const discountData = await getBasicProductDiscounts(productInfo.productId);

    // 计算新价格
    const priceData = calculateDiscountedPrices(productInfo, discountData);

    // 只有当价格确实发生变化时才更新显示
    if (priceData.finalSalePrice !== productInfo.salePrice ||
        priceData.finalDiscount !== productInfo.currentDiscount) {

      // 更新显示
      updateProductCardDisplay(productInfo, priceData);

      console.log(`Applied ${priceData.hasApiDiscount ? 'API' : 'existing'} discount to product ${productInfo.productId}`);
    }

    // 更新存储的商品信息
    productInfoMap.set(productInfo.productId, {
      ...productInfo,
      ...priceData,
      lastUpdated: Date.now()
    });

  } catch (error) {
    console.error('Error processing product discount:', error);
  }
}

/**
 * 查找包含data-product-id属性的祖先元素
 * @param {Element} element - 起始元素
 * @returns {Element|null} 找到的商品卡片元素
 */
function findProductCard(element) {
  let current = element;
  while (current && current !== document.body) {
    if (current.hasAttribute && current.hasAttribute('data-product-id')) {
      return current;
    }
    current = current.parentElement;
  }
  return null;
}

/**
 * 处理card__badge元素的变化
 * @param {Element} badgeElement - 折扣徽章元素
 */
function handleBadgeChange(badgeElement) {
  const productCard = findProductCard(badgeElement);
  if (productCard) {
    console.log('Badge change detected for product:', productCard.getAttribute('data-product-id'));
    processProductDiscount(productCard);
  }
}

/**
 * MutationObserver回调函数
 * @param {MutationRecord[]} mutations - 变化记录数组
 */
function handleMutations(mutations) {
  const processedCards = new Set();

  mutations.forEach(mutation => {
    // 处理新增的节点
    if (mutation.type === 'childList') {
      mutation.addedNodes.forEach(node => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          // 检查新增的节点是否是card__badge
          if (node.classList && node.classList.contains('card__badge')) {
            handleBadgeChange(node);
          }

          // 检查新增节点的子元素中是否包含card__badge
          const badgeElements = node.querySelectorAll && node.querySelectorAll(CONFIG.SELECTORS.BADGE);
          if (badgeElements) {
            badgeElements.forEach(badge => {
              const productCard = findProductCard(badge);
              if (productCard && !processedCards.has(productCard)) {
                processedCards.add(productCard);
                handleBadgeChange(badge);
              }
            });
          }
        }
      });
    }

    // 处理内容变化
    if (mutation.type === 'characterData' || mutation.type === 'childList') {
      const target = mutation.target;

      // 检查变化的节点是否在card__badge内
      let badgeElement = null;
      if (target.classList && target.classList.contains('card__badge')) {
        badgeElement = target;
      } else {
        badgeElement = target.closest && target.closest(CONFIG.SELECTORS.BADGE);
      }

      if (badgeElement) {
        const productCard = findProductCard(badgeElement);
        if (productCard && !processedCards.has(productCard)) {
          processedCards.add(productCard);
          handleBadgeChange(badgeElement);
        }
      }
    }
  });
}

/**
 * 初始化MutationObserver
 */
function initializeMutationObserver() {
  // 创建MutationObserver实例
  const observer = new MutationObserver(handleMutations);

  // 配置观察选项
  const config = {
    childList: true,        // 观察子节点的变化
    subtree: true,          // 观察所有后代节点
    characterData: true,    // 观察文本内容变化
    attributes: false       // 不观察属性变化
  };

  // 开始观察
  observer.observe(document.body, config);

  console.log('MutationObserver initialized for card__badge elements');
  return observer;
}

/**
 * 处理页面上现有的商品卡片
 */
function processExistingProducts() {
  const productCards = document.querySelectorAll(CONFIG.SELECTORS.PRODUCT_CARD);
  console.log(`Found ${productCards.length} existing product cards`);

  productCards.forEach(productCard => {
    const badge = productCard.querySelector(CONFIG.SELECTORS.BADGE);
    if (badge) {
      processProductDiscount(productCard);
    }
  });
}

/**
 * 初始化折扣系统
 */
function initializeDiscountSystem() {
  console.log('Initializing discount system...');

  // 处理现有商品
  processExistingProducts();

  // 初始化MutationObserver
  const observer = initializeMutationObserver();

  // 返回observer以便后续可以停止观察
  return observer;
}

// 全局变量存储observer
let discountObserver = null;

/**
 * 停止折扣系统
 */
function stopDiscountSystem() {
  if (discountObserver) {
    discountObserver.disconnect();
    discountObserver = null;
    console.log('Discount system stopped');
  }
}

/**
 * 重启折扣系统
 */
function restartDiscountSystem() {
  stopDiscountSystem();
  discountObserver = initializeDiscountSystem();
}

/**
 * 清除所有已处理的商品信息
 */
function clearProductCache() {
  productInfoMap.clear();
  console.log('Product cache cleared');
}

/**
 * 强制重新处理所有商品（清除缓存后重新处理）
 */
function forceReprocessAllProducts() {
  clearProductCache();
  processExistingProducts();
  console.log('All products reprocessed');
}

// 页面加载完成后自动初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    discountObserver = initializeDiscountSystem();
  });
} else {
  // 如果页面已经加载完成，立即初始化
  discountObserver = initializeDiscountSystem();
}

// 暴露全局方法供调试使用
window.DiscountSystem = {
  init: initializeDiscountSystem,
  stop: stopDiscountSystem,
  restart: restartDiscountSystem,
  processExisting: processExistingProducts,
  clearCache: clearProductCache,
  forceReprocess: forceReprocessAllProducts,
  productMap: productInfoMap,
  config: CONFIG,

  // 调试方法
  getProductInfo: (productId) => productInfoMap.get(productId),
  getAllProducts: () => Array.from(productInfoMap.entries()),
  getStats: () => ({
    totalProducts: productInfoMap.size,
    processedWithApiDiscount: Array.from(productInfoMap.values()).filter(p => p.hasApiDiscount).length
  })
};

console.log('Discount system loaded. Use window.DiscountSystem for debugging.');